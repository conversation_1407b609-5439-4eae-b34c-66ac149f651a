#include "Compression.h"
#include "Logger.h"
#include "ExternalDependencies.h"
#include <algorithm>
#include <chrono>
#include <set>
#include <map>
#include <cmath>
// Note: compressapi.h not available in MinGW, using alternative implementation

namespace Compression
{
    // RLE Compressor Implementation
    CompressionResult RLECompressor::compress(const std::vector<uint8_t>& data)
    {
        CompressionResult result;
        result.type = CompressionType::RLE;
        result.originalSize = data.size();
        result.success = false;

        if (data.empty())
        {
            result.compressedSize = 0;
            result.ratio = 0.0;
            result.success = true;
            return result;
        }

        std::vector<uint8_t> compressed;
        compressed.reserve(data.size()); // Reserve space

        size_t i = 0;
        while (i < data.size())
        {
            uint8_t currentByte = data[i];
            size_t runLength = 1;

            // Count consecutive identical bytes
            while (i + runLength < data.size() &&
                   data[i + runLength] == currentByte &&
                   runLength < 255)
            {
                runLength++;
            }

            if (runLength >= 3) // Use RLE for runs of 3 or more
            {
                compressed.push_back(0xFF); // RLE marker
                compressed.push_back(static_cast<uint8_t>(runLength));
                compressed.push_back(currentByte);
            }
            else // Store literal bytes
            {
                for (size_t j = 0; j < runLength; j++)
                {
                    if (data[i + j] == 0xFF) // Escape marker byte
                    {
                        compressed.push_back(0xFF);
                        compressed.push_back(0x00); // Escaped 0xFF
                    }
                    else
                    {
                        compressed.push_back(data[i + j]);
                    }
                }
            }

            i += runLength;
        }

        result.data = std::move(compressed);
        result.compressedSize = result.data.size();
        result.ratio = static_cast<double>(result.compressedSize) / result.originalSize;
        result.success = true;

        return result;
    }

    std::vector<uint8_t> RLECompressor::decompress(const std::vector<uint8_t>& compressedData, size_t originalSize)
    {
        std::vector<uint8_t> decompressed;
        decompressed.reserve(originalSize);

        size_t i = 0;
        while (i < compressedData.size())
        {
            if (compressedData[i] == 0xFF && i + 1 < compressedData.size())
            {
                if (compressedData[i + 1] == 0x00) // Escaped 0xFF
                {
                    decompressed.push_back(0xFF);
                    i += 2;
                }
                else if (i + 2 < compressedData.size()) // RLE sequence
                {
                    uint8_t runLength = compressedData[i + 1];
                    uint8_t value = compressedData[i + 2];

                    for (int j = 0; j < runLength; j++)
                    {
                        decompressed.push_back(value);
                    }
                    i += 3;
                }
                else
                {
                    break; // Invalid data
                }
            }
            else
            {
                decompressed.push_back(compressedData[i]);
                i++;
            }
        }

        return decompressed;
    }

    // Windows Compressor Implementation (Fallback to RLE for MinGW compatibility)
    CompressionResult WindowsCompressor::compress(const std::vector<uint8_t>& data)
    {
        CompressionResult result;
        result.type = CompressionType::Windows;
        result.originalSize = data.size();
        result.success = false;

        if (data.empty())
        {
            result.compressedSize = 0;
            result.ratio = 0.0;
            result.success = true;
            return result;
        }

        // Fallback to RLE compression for MinGW compatibility
        RLECompressor rleCompressor;
        auto rleResult = rleCompressor.compress(data);

        if (rleResult.success)
        {
            result.data = std::move(rleResult.data);
            result.compressedSize = rleResult.compressedSize;
            result.ratio = rleResult.ratio;
            result.success = true;

            LOG_DEBUG("Windows compressor fallback to RLE: " + std::to_string(result.originalSize) +
                     " -> " + std::to_string(result.compressedSize) + " bytes");
        }
        else
        {
            LOG_ERROR("Windows compressor fallback failed");
        }

        return result;
    }

    std::vector<uint8_t> WindowsCompressor::decompress(const std::vector<uint8_t>& compressedData, size_t originalSize)
    {
        if (compressedData.empty())
        {
            return std::vector<uint8_t>();
        }

        // Fallback to RLE decompression for MinGW compatibility
        RLECompressor rleCompressor;
        auto result = rleCompressor.decompress(compressedData, originalSize);

        LOG_DEBUG("Windows decompressor fallback to RLE: " + std::to_string(compressedData.size()) +
                 " -> " + std::to_string(result.size()) + " bytes");

        return result;
    }

    // LZ77 Compressor Implementation (simplified)
    CompressionResult LZ77Compressor::compress(const std::vector<uint8_t>& data)
    {
        CompressionResult result;
        result.type = CompressionType::LZ77;
        result.originalSize = data.size();
        result.success = false;

        // For now, fall back to RLE for simplicity
        // A full LZ77 implementation would be quite complex
        RLECompressor rle;
        return rle.compress(data);
    }

    std::vector<uint8_t> LZ77Compressor::decompress(const std::vector<uint8_t>& compressedData, size_t originalSize)
    {
        // For now, fall back to RLE for simplicity
        RLECompressor rle;
        return rle.decompress(compressedData, originalSize);
    }

    LZ77Compressor::Match LZ77Compressor::findLongestMatch(const std::vector<uint8_t>& data, size_t position, size_t windowSize)
    {
        Match bestMatch = {0, 0};

        size_t searchStart = (position >= windowSize) ? position - windowSize : 0;

        for (size_t i = searchStart; i < position; i++)
        {
            size_t matchLength = 0;
            while (position + matchLength < data.size() &&
                   i + matchLength < position &&
                   data[i + matchLength] == data[position + matchLength])
            {
                matchLength++;
            }

            if (static_cast<int>(matchLength) > bestMatch.length)
            {
                bestMatch.distance = static_cast<int>(position - i);
                bestMatch.length = static_cast<int>(matchLength);
            }
        }

        return bestMatch;
    }

    // LZ4 Compressor Implementation
    CompressionResult LZ4Compressor::compress(const std::vector<uint8_t>& data)
    {
        CompressionResult result;
        result.type = CompressionType::LZ4;
        result.originalSize = data.size();
        result.success = false;

        if (data.empty())
        {
            result.compressedSize = 0;
            result.ratio = 0.0;
            result.success = true;
            return result;
        }

        try
        {
            // Calculate maximum compressed size (estimate)
            int maxCompressedSize = static_cast<int>(data.size() + data.size() / 255 + 16);
            std::vector<uint8_t> compressed(maxCompressedSize);

            // Compress data
            int compressedSize = LZ4::compress_default(
                reinterpret_cast<const char*>(data.data()),
                reinterpret_cast<char*>(compressed.data()),
                static_cast<int>(data.size()),
                maxCompressedSize
            );

            if (compressedSize > 0)
            {
                // Resize to actual compressed size
                compressed.resize(compressedSize);
                result.data = std::move(compressed);
                result.compressedSize = compressedSize;
                result.ratio = static_cast<double>(compressedSize) / data.size();
                result.success = true;

                LOG_DEBUG("LZ4 compressed " + std::to_string(data.size()) +
                         " bytes to " + std::to_string(compressedSize) +
                         " bytes (ratio: " + std::to_string(result.ratio) + ")");
            }
            else
            {
                LOG_ERROR("LZ4 compression failed");
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("LZ4 compression error: " + std::string(e.what()));
        }

        return result;
    }

    std::vector<uint8_t> LZ4Compressor::decompress(const std::vector<uint8_t>& compressedData, size_t originalSize)
    {
        if (compressedData.empty() || originalSize == 0)
        {
            return std::vector<uint8_t>();
        }

        try
        {
            std::vector<uint8_t> decompressed(originalSize);

            int decompressedSize = LZ4::decompress_safe(
                reinterpret_cast<const char*>(compressedData.data()),
                reinterpret_cast<char*>(decompressed.data()),
                static_cast<int>(compressedData.size()),
                static_cast<int>(originalSize)
            );

            if (decompressedSize > 0)
            {
                LOG_DEBUG("LZ4 decompressed " + std::to_string(compressedData.size()) +
                         " bytes to " + std::to_string(decompressedSize) + " bytes");
                return decompressed;
            }
            else
            {
                LOG_ERROR("LZ4 decompression failed");
            }
        }
        catch (const std::exception& e)
        {
            LOG_ERROR("LZ4 decompression error: " + std::string(e.what()));
        }

        return std::vector<uint8_t>();
    }

    // Compression Manager Implementation
    CompressionManager::CompressionManager()
        : m_preferredType(CompressionType::LZ4)  // Default to LZ4
    {
        m_lz4Compressor = std::make_unique<LZ4Compressor>();
        m_rleCompressor = std::make_unique<RLECompressor>();
        m_windowsCompressor = std::make_unique<WindowsCompressor>();
        resetStatistics();
    }

    CompressionManager::~CompressionManager() = default;

    CompressionResult CompressionManager::compressScreenData(const std::vector<uint8_t>& data)
    {
        auto startTime = std::chrono::high_resolution_clock::now();

        ICompressor* compressor = chooseBestCompressor(data);
        CompressionResult result = compressor->compress(data);

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        double timeMs = duration.count() / 1000.0;

        updateCompressionStats(result, timeMs);

        return result;
    }

    std::vector<uint8_t> CompressionManager::decompress(const std::vector<uint8_t>& compressedData,
                                                       CompressionType type,
                                                       size_t originalSize)
    {
        auto startTime = std::chrono::high_resolution_clock::now();

        std::vector<uint8_t> result;

        switch (type)
        {
            case CompressionType::RLE:
                result = m_rleCompressor->decompress(compressedData, originalSize);
                break;
            case CompressionType::LZ77:
                result = m_lz77Compressor->decompress(compressedData, originalSize);
                break;
            case CompressionType::Windows:
                result = m_windowsCompressor->decompress(compressedData, originalSize);
                break;
            case CompressionType::LZ4:
                result = m_lz4Compressor->decompress(compressedData, originalSize);
                break;
            default:
                result = std::vector<uint8_t>(compressedData.begin(), compressedData.end());
                break;
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
        double timeMs = duration.count() / 1000.0;

        updateDecompressionStats(originalSize, compressedData.size(), timeMs);

        return result;
    }

    void CompressionManager::setPreferredCompression(CompressionType type)
    {
        m_preferredType = type;
    }

    void CompressionManager::resetStatistics()
    {
        m_stats = {};
    }

    ICompressor* CompressionManager::chooseBestCompressor(const std::vector<uint8_t>& data)
    {
        // For screen data, LZ4 is usually most effective
        return m_lz4Compressor.get();
    }

    void CompressionManager::updateCompressionStats(const CompressionResult& result, double timeMs)
    {
        if (result.success)
        {
            m_stats.totalBytesCompressed += result.originalSize;
            m_stats.totalCompressionOperations++;
            m_stats.totalCompressionTime += timeMs;

            // Update average compression ratio
            double newRatio = result.ratio;
            m_stats.averageCompressionRatio =
                (m_stats.averageCompressionRatio * (m_stats.totalCompressionOperations - 1) + newRatio) /
                m_stats.totalCompressionOperations;
        }
    }

    void CompressionManager::updateDecompressionStats(size_t originalSize, size_t compressedSize, double timeMs)
    {
        (void)compressedSize; // Suppress unused parameter warning
        m_stats.totalBytesDecompressed += originalSize;
        m_stats.totalDecompressionOperations++;
        m_stats.totalDecompressionTime += timeMs;
    }

    // Utility functions implementation
    namespace Utils
    {
        double calculateCompressionRatio(size_t originalSize, size_t compressedSize)
        {
            if (originalSize == 0) return 0.0;
            return static_cast<double>(compressedSize) / static_cast<double>(originalSize);
        }

        bool shouldCompress(const std::vector<uint8_t>& data, size_t threshold)
        {
            // Don't compress small data
            if (data.size() < threshold) {
                return false;
            }

            // Analyze data to see if compression would be beneficial
            DataAnalysis analysis = analyzeData(data);

            // If entropy is too high, compression won't help much
            if (analysis.entropy > 7.5) {
                return false;
            }

            // If there are repeating patterns or low unique byte count, compress
            return analysis.hasRepeatingPatterns ||
                   (analysis.uniqueBytes < data.size() / 4) ||
                   (analysis.longestRun > 10);
        }

        DataAnalysis analyzeData(const std::vector<uint8_t>& data)
        {
            DataAnalysis analysis = {};

            if (data.empty()) {
                return analysis;
            }

            // Count unique bytes
            std::set<uint8_t> uniqueBytes;
            for (uint8_t byte : data) {
                uniqueBytes.insert(byte);
            }
            analysis.uniqueBytes = uniqueBytes.size();

            // Find longest run
            size_t currentRun = 1;
            size_t maxRun = 1;
            for (size_t i = 1; i < data.size(); i++) {
                if (data[i] == data[i-1]) {
                    currentRun++;
                } else {
                    maxRun = std::max(maxRun, currentRun);
                    currentRun = 1;
                }
            }
            maxRun = std::max(maxRun, currentRun);
            analysis.longestRun = maxRun;

            // Simple entropy calculation
            std::map<uint8_t, size_t> frequency;
            for (uint8_t byte : data) {
                frequency[byte]++;
            }

            double entropy = 0.0;
            for (const auto& pair : frequency) {
                double prob = static_cast<double>(pair.second) / data.size();
                if (prob > 0) {
                    entropy -= prob * std::log2(prob);
                }
            }
            analysis.entropy = entropy;

            // Check for repeating patterns
            analysis.hasRepeatingPatterns = (maxRun > 5) || (analysis.uniqueBytes < data.size() / 8);

            return analysis;
        }
    }
}
