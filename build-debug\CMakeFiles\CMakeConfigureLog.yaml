
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.9.8+b34f75857 for .NET Framework
      Build started 25/05/2025 1:41:42 am.
      
      Project "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\CL.exe /c /I"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "D:\\Project\\SuperBot\\Client\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe" "D:\\Project\\SuperBot\\Client\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.70
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/Project/SuperBot/Client/build-debug/CMakeFiles/4.0.2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/Project/SuperBot/Client/build-debug/CMakeFiles/CMakeScratch/TryCompile-ru6wpc"
      binary: "D:/Project/SuperBot/Client/build-debug/CMakeFiles/CMakeScratch/TryCompile-ru6wpc"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Od /Zi /RTC1 /MDd"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "D:/Project/SuperBot/Client/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "D:/Project/SuperBot/Client/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/Project/SuperBot/Client/build-debug/CMakeFiles/CMakeScratch/TryCompile-ru6wpc'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0ba9e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.9.8+b34f75857 for .NET Framework
        Build started 25/05/2025 1:41:44 am.
        
        Project "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\CMakeScratch\\TryCompile-ru6wpc\\cmTC_0ba9e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_0ba9e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\CMakeScratch\\TryCompile-ru6wpc\\Debug\\".
          Creating directory "cmTC_0ba9e.dir\\Debug\\cmTC_0ba9e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_0ba9e.dir\\Debug\\cmTC_0ba9e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_0ba9e.dir\\Debug\\cmTC_0ba9e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0ba9e.dir\\Debug\\\\" /Fd"cmTC_0ba9e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.39.33523 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0ba9e.dir\\Debug\\\\" /Fd"cmTC_0ba9e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.39.33519\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\CMakeScratch\\TryCompile-ru6wpc\\Debug\\cmTC_0ba9e.exe" /INCREMENTAL /ILK:"cmTC_0ba9e.dir\\Debug\\cmTC_0ba9e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/Project/SuperBot/Client/build-debug/CMakeFiles/CMakeScratch/TryCompile-ru6wpc/Debug/cmTC_0ba9e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/Project/SuperBot/Client/build-debug/CMakeFiles/CMakeScratch/TryCompile-ru6wpc/Debug/cmTC_0ba9e.lib" /MACHINE:X64  /machine:x64 cmTC_0ba9e.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_0ba9e.vcxproj -> D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\CMakeScratch\\TryCompile-ru6wpc\\Debug\\cmTC_0ba9e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_0ba9e.dir\\Debug\\cmTC_0ba9e.tlog\\unsuccessfulbuild".
          Touching "cmTC_0ba9e.dir\\Debug\\cmTC_0ba9e.tlog\\cmTC_0ba9e.lastbuildstate".
        Done Building Project "D:\\Project\\SuperBot\\Client\\build-debug\\CMakeFiles\\CMakeScratch\\TryCompile-ru6wpc\\cmTC_0ba9e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.60
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.39.33519/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.39.33519/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.39.33523.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
