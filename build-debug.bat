@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM SuperBot Client - Debug Build Script
REM ============================================================================
REM This script builds the SuperBot Client project in Debug mode using CMake
REM with Visual Studio 2022 and vcpkg for dependency management.
REM ============================================================================

echo.
echo ============================================================================
echo                    SuperBot Client - Debug Build Script
echo ============================================================================
echo.

REM Set script directory and project paths
set "SCRIPT_DIR=%~dp0"
set "PROJECT_DIR=%SCRIPT_DIR%"
set "BUILD_DIR=%PROJECT_DIR%build-debug"
set "VCPKG_DIR=%PROJECT_DIR%vcpkg"
set "VCPKG_TOOLCHAIN=%VCPKG_DIR%\scripts\buildsystems\vcpkg.cmake"

REM Configuration variables
set "CMAKE_PRESET=debug"
set "BUILD_PRESET=debug"
set "BUILD_TYPE=Debug"
set "GENERATOR=Visual Studio 17 2022"
set "PLATFORM=x64"
set "VCPKG_TRIPLET=x64-windows"

echo [INFO] Project Directory: %PROJECT_DIR%
echo [INFO] Build Directory: %BUILD_DIR%
echo [INFO] Build Type: %BUILD_TYPE%
echo [INFO] Generator: %GENERATOR%
echo [INFO] Platform: %PLATFORM%
echo [INFO] vcpkg Triplet: %VCPKG_TRIPLET%
echo.

REM Check if CMake is available
echo [INFO] Checking for CMake...
cmake --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] CMake not found! Please install CMake and add it to PATH.
    echo [ERROR] Download from: https://cmake.org/download/
    pause
    exit /b 1
)
echo [OK] CMake found.

REM Check if Visual Studio 2022 is available
echo [INFO] Checking for Visual Studio 2022...
where msbuild >nul 2>&1
if errorlevel 1 (
    echo [WARNING] MSBuild not found in PATH. Attempting to locate Visual Studio...
    
    REM Try to find Visual Studio 2022
    set "VS_PATH="
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat" (
        set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat" (
        set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\Tools\VsDevCmd.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat" (
        set "VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\Tools\VsDevCmd.bat"
    )
    
    if defined VS_PATH (
        echo [INFO] Found Visual Studio 2022 at: !VS_PATH!
        echo [INFO] Setting up Visual Studio environment...
        call "!VS_PATH!" -arch=x64 -host_arch=x64
    ) else (
        echo [ERROR] Visual Studio 2022 not found!
        echo [ERROR] Please install Visual Studio 2022 with C++ development tools.
        pause
        exit /b 1
    )
) else (
    echo [OK] MSBuild found in PATH.
)

REM Check and setup vcpkg
echo [INFO] Checking vcpkg setup...
if not exist "%VCPKG_DIR%" (
    echo [ERROR] vcpkg directory not found at: %VCPKG_DIR%
    echo [ERROR] Please ensure vcpkg is properly set up.
    pause
    exit /b 1
)

if not exist "%VCPKG_DIR%\vcpkg.exe" (
    echo [INFO] vcpkg.exe not found. Running bootstrap...
    pushd "%VCPKG_DIR%"
    if exist "bootstrap-vcpkg.bat" (
        call bootstrap-vcpkg.bat
        if errorlevel 1 (
            echo [ERROR] Failed to bootstrap vcpkg!
            popd
            pause
            exit /b 1
        )
    ) else (
        echo [ERROR] bootstrap-vcpkg.bat not found in vcpkg directory!
        popd
        pause
        exit /b 1
    )
    popd
)
echo [OK] vcpkg is ready.

REM Check if vcpkg toolchain file exists
if not exist "%VCPKG_TOOLCHAIN%" (
    echo [ERROR] vcpkg toolchain file not found at: %VCPKG_TOOLCHAIN%
    pause
    exit /b 1
)
echo [OK] vcpkg toolchain file found.

REM Create build directory if it doesn't exist
if not exist "%BUILD_DIR%" (
    echo [INFO] Creating build directory: %BUILD_DIR%
    mkdir "%BUILD_DIR%"
    if errorlevel 1 (
        echo [ERROR] Failed to create build directory!
        pause
        exit /b 1
    )
)

REM Change to project directory
pushd "%PROJECT_DIR%"

echo.
echo ============================================================================
echo                           CONFIGURE PHASE
echo ============================================================================
echo.

REM Configure the project using CMake preset
echo [INFO] Configuring project with CMake preset: %CMAKE_PRESET%
cmake --preset %CMAKE_PRESET%
if errorlevel 1 (
    echo [ERROR] CMake configuration failed!
    echo [ERROR] Check the output above for details.
    popd
    pause
    exit /b 1
)
echo [OK] Configuration completed successfully.

echo.
echo ============================================================================
echo                             BUILD PHASE
echo ============================================================================
echo.

REM Build the project using CMake preset
echo [INFO] Building project with build preset: %BUILD_PRESET%
cmake --build --preset %BUILD_PRESET%
if errorlevel 1 (
    echo [ERROR] Build failed!
    echo [ERROR] Check the output above for details.
    popd
    pause
    exit /b 1
)
echo [OK] Build completed successfully.

echo.
echo ============================================================================
echo                           BUILD SUMMARY
echo ============================================================================
echo.

REM Check if executable was created
set "EXE_PATH=%BUILD_DIR%\bin\Debug\SuperBotClient.exe"
if exist "%EXE_PATH%" (
    echo [OK] Executable created: %EXE_PATH%
    
    REM Get file size and modification time
    for %%F in ("%EXE_PATH%") do (
        echo [INFO] File size: %%~zF bytes
        echo [INFO] Last modified: %%~tF
    )
) else (
    echo [WARNING] Executable not found at expected location: %EXE_PATH%
    echo [INFO] Searching for executable in build directory...
    
    REM Search for the executable in the build directory
    for /r "%BUILD_DIR%" %%F in (SuperBotClient.exe) do (
        if exist "%%F" (
            echo [FOUND] Executable at: %%F
        )
    )
)

REM Check for debug symbols
set "PDB_PATH=%BUILD_DIR%\bin\Debug\SuperBotClient.pdb"
if exist "%PDB_PATH%" (
    echo [OK] Debug symbols created: %PDB_PATH%
) else (
    echo [WARNING] Debug symbols not found at: %PDB_PATH%
)

echo.
echo [INFO] Build configuration: %BUILD_TYPE%
echo [INFO] Build directory: %BUILD_DIR%
echo [INFO] Generator used: %GENERATOR%
echo [INFO] Platform: %PLATFORM%
echo [INFO] vcpkg triplet: %VCPKG_TRIPLET%

popd

echo.
echo ============================================================================
echo                         BUILD COMPLETED
echo ============================================================================
echo.
echo [SUCCESS] Debug build completed successfully!
echo.
echo To run the application:
echo   cd "%BUILD_DIR%\bin\Debug"
echo   SuperBotClient.exe
echo.
echo To debug in Visual Studio:
echo   Open: %BUILD_DIR%\SuperBotClient.sln
echo.

pause
