# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: detect_compiler
# Configurations: Release
# =============================================================================
# =============================================================================

#############################################
# localized /showIncludes string

msvc_deps_prefix = Note: including file: 


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SD:\Project\SuperBot\Client\vcpkg\scripts\detect_compiler -BD:\Project\SuperBot\Client\vcpkg\buildtrees\detect_compiler\x64-windows-rel
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\Project\SuperBot\Client\vcpkg\downloads\tools\ninja\1.12.1-windows\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\Project\SuperBot\Client\vcpkg\downloads\tools\ninja\1.12.1-windows\ninja.exe -t targets
  description = All primary targets available:

