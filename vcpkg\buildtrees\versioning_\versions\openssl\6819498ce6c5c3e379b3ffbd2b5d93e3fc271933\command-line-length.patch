diff --git a/Configurations/unix-Makefile.tmpl b/Configurations/unix-Makefile.tmpl
index 8ddb128..52b9ad6 100644
--- a/Configurations/unix-Makefile.tmpl
+++ b/Configurations/unix-Makefile.tmpl
@@ -1961,7 +1961,7 @@ EOF
       my @objs = map { platform->obj($_) } @{$args{objs}};
       my $deps = join(" \\\n" . ' ' x (length($lib) + 2),
                       fill_lines(' ', $COLUMNS - length($lib) - 2, @objs));
-      my $max_per_call = 500;
+      my $max_per_call = ($^O eq 'msys') ? 80 : 500;
       my @objs_grouped;
       push @objs_grouped, join(" ", splice @objs, 0, $max_per_call) while @objs;
       my $fill_lib =
