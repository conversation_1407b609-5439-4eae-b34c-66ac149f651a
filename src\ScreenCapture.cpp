#include "ScreenCapture.h"
#include "Logger.h"
#include "Compression.h"
#include <sstream>
#include <iomanip>
#include <algorithm>

// Windows-specific data structure
struct ScreenCapture::WindowsData
{
    HDC desktopDC;
    HDC memoryDC;
    HBITMAP bitmap;
    BITMAPINFO bitmapInfo;
    int screenWidth;
    int screenHeight;
    std::vector<uint8_t> bitmapData;
};

ScreenCapture::ScreenCapture()
    : m_capturing(false), m_frameRate(DEFAULT_FRAME_RATE), m_quality(DEFAULT_SCREEN_QUALITY), m_format(ScreenFormat::RGB32), m_compressionEnabled(true), m_deltaFramesEnabled(true), m_adaptiveQuality(true), m_hardwareAcceleration(false), m_imageResizeEnabled(true), m_imageScalePercent(1.0f) // Default to 100% scale
      ,
      m_activeMonitor(0), m_useCustomRegion(false), m_maxFrameSize(MAX_FRAME_SIZE), m_targetFrameRate(DEFAULT_FRAME_RATE), m_currentFrameRate(0), m_averageFrameSize(0), m_cpuUsage(0.0), m_frameCount(0), m_totalFrameSize(0), m_windowsData(std::make_unique<WindowsData>()),
      // Performance optimization variables
      m_lastFrameTime(std::chrono::steady_clock::now()), m_frameSkipCount(0), m_adaptiveFrameRate(DEFAULT_FRAME_RATE), m_networkLatency(0), m_lastQualityAdjustment(std::chrono::steady_clock::now())
{
    m_lastCaptureTime = std::chrono::steady_clock::now();
    m_lastStatsUpdate = std::chrono::steady_clock::now();
    m_captureStartTime = std::chrono::steady_clock::now();

    InitializeCriticalSection(&m_captureCriticalSection);

    // Initialize compression manager
    m_compressionManager = std::make_unique<Compression::CompressionManager>();
    m_compressionManager->setPreferredCompression(Compression::CompressionType::LZ4);
}

ScreenCapture::~ScreenCapture()
{
    shutdown();
    DeleteCriticalSection(&m_captureCriticalSection);
}

bool ScreenCapture::initialize()
{
    LOG_INFO("Initializing screen capture");

    if (!initializePlatformCapture())
    {
        LOG_ERROR("Failed to initialize platform-specific screen capture");
        return false;
    }

    detectMonitors();
    LOG_INFO("Screen capture initialized successfully");
    return true;
}

void ScreenCapture::shutdown()
{
    LOG_INFO("Shutting down screen capture");
    stop();
    cleanupPlatformCapture();
}

bool ScreenCapture::start()
{
    LOG_INFO("Starting screen capture");
    m_capturing = true;
    m_captureStartTime = std::chrono::steady_clock::now();
    return true;
}

void ScreenCapture::stop()
{
    LOG_INFO("Stopping screen capture");
    m_capturing = false;
}

void ScreenCapture::processFrame()
{
    if (!m_capturing)
    {
        LOG_DEBUG("processFrame called but not capturing");
        return;
    }

    LOG_DEBUG("processFrame called - capturing active");

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastCapture = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastCaptureTime);

    // Adaptive frame rate based on performance
    updateAdaptiveFrameRate();

    // Check if it's time for the next frame using adaptive frame rate
    int frameInterval = 1000 / m_adaptiveFrameRate;
    if (timeSinceLastCapture.count() < frameInterval)
    {
        return;
    }

    // Track actual frame timing for performance metrics
    auto actualFrameTime = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastFrameTime);
    m_lastFrameTime = now;

    m_lastCaptureTime = now;

    try
    {
        ScreenFrame frame = captureScreen();

        if (shouldSendFrame(frame))
        {
            m_frameCount++;
            m_totalFrameSize += static_cast<int>(frame.data.size());

            LOG_DEBUG("Screen frame captured: " + std::to_string(frame.region.width) + "x" +
                      std::to_string(frame.region.height) + ", data size: " + std::to_string(frame.data.size()));

            // Debug: Verify frame region before callback
            LOG_INFO("Before callback - frame.region: x=" + std::to_string(frame.region.x) +
                     ", y=" + std::to_string(frame.region.y) +
                     ", width=" + std::to_string(frame.region.width) +
                     ", height=" + std::to_string(frame.region.height));

            if (m_frameReadyCallback)
            {
                LOG_DEBUG("Calling frame ready callback");
                m_frameReadyCallback(frame);
                LOG_DEBUG("Frame ready callback completed");
            }
            else
            {
                LOG_WARNING("No frame ready callback set");
            }
        }
        else
        {
            LOG_DEBUG("Frame not sent (shouldSendFrame returned false)");
        }

        // Update statistics and adaptive quality periodically
        auto timeSinceStats = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastStatsUpdate);
        if (timeSinceStats.count() >= STATS_UPDATE_INTERVAL)
        {
            updatePerformanceStats();

            // Perform adaptive quality adjustment
            if (m_adaptiveQuality)
            {
                adjustQuality();
            }

            m_lastStatsUpdate = now;

            // Log performance metrics every 5 seconds
            static int logCounter = 0;
            if (++logCounter % 5 == 0)
            {
                LOG_INFO("Performance: " + std::to_string(m_currentFrameRate) + " FPS, " +
                        "Adaptive: " + std::to_string(m_adaptiveFrameRate) + " FPS, " +
                        "Skipped: " + std::to_string(m_frameSkipCount) + " frames, " +
                        "Avg size: " + std::to_string(m_averageFrameSize) + " bytes");
            }
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Screen capture error: " + std::string(e.what()));
        if (m_errorCallback)
        {
            m_errorCallback(e.what());
        }
    }
}

std::vector<MonitorInfo> ScreenCapture::getMonitors() const
{
    return m_monitors;
}

void ScreenCapture::setCaptureRegion(const Rect &region)
{
    EnterCriticalSection(&m_captureCriticalSection);
    m_captureRegion = region;
    m_useCustomRegion = true;
    LeaveCriticalSection(&m_captureCriticalSection);
}

void ScreenCapture::resetCaptureRegion()
{
    EnterCriticalSection(&m_captureCriticalSection);
    m_useCustomRegion = false;
    LeaveCriticalSection(&m_captureCriticalSection);
}

void ScreenCapture::setFrameReadyCallback(std::function<void(const ScreenFrame &)> callback)
{
    m_frameReadyCallback = callback;
}

void ScreenCapture::setErrorCallback(std::function<void(const std::string &)> callback)
{
    m_errorCallback = callback;
}

bool ScreenCapture::initializePlatformCapture()
{
    // Initialize Windows GDI capture
    m_windowsData->desktopDC = GetDC(nullptr);
    if (!m_windowsData->desktopDC)
    {
        LOG_ERROR("Failed to get desktop DC");
        return false;
    }

    m_windowsData->memoryDC = CreateCompatibleDC(m_windowsData->desktopDC);
    if (!m_windowsData->memoryDC)
    {
        LOG_ERROR("Failed to create compatible DC");
        return false;
    }

    // Get screen dimensions using GetSystemMetrics
    m_windowsData->screenWidth = GetSystemMetrics(SM_CXSCREEN);
    m_windowsData->screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // Validate screen dimensions
    if (m_windowsData->screenWidth <= 0 || m_windowsData->screenHeight <= 0)
    {
        LOG_ERROR("Invalid screen dimensions: " + std::to_string(m_windowsData->screenWidth) +
                  "x" + std::to_string(m_windowsData->screenHeight));
        return false;
    }

    LOG_INFO("Detected screen dimensions: " + std::to_string(m_windowsData->screenWidth) +
             "x" + std::to_string(m_windowsData->screenHeight));

    // Create bitmap with actual screen dimensions
    m_windowsData->bitmap = CreateCompatibleBitmap(m_windowsData->desktopDC,
                                                   m_windowsData->screenWidth, m_windowsData->screenHeight);
    if (!m_windowsData->bitmap)
    {
        LOG_ERROR("Failed to create compatible bitmap");
        return false;
    }

    SelectObject(m_windowsData->memoryDC, m_windowsData->bitmap);

    // Setup bitmap info
    ZeroMemory(&m_windowsData->bitmapInfo, sizeof(BITMAPINFO));
    m_windowsData->bitmapInfo.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    m_windowsData->bitmapInfo.bmiHeader.biWidth = m_windowsData->screenWidth;
    m_windowsData->bitmapInfo.bmiHeader.biHeight = -m_windowsData->screenHeight; // Top-down
    m_windowsData->bitmapInfo.bmiHeader.biPlanes = 1;
    m_windowsData->bitmapInfo.bmiHeader.biBitCount = 32;
    m_windowsData->bitmapInfo.bmiHeader.biCompression = BI_RGB;

    // Allocate bitmap data buffer
    size_t dataSize = m_windowsData->screenWidth * m_windowsData->screenHeight * 4;
    m_windowsData->bitmapData.resize(dataSize);

    LOG_INFO("Initialized screen capture with " + std::to_string(dataSize) + " bytes buffer");
    return true;
}

void ScreenCapture::cleanupPlatformCapture()
{
    if (m_windowsData->bitmap)
    {
        DeleteObject(m_windowsData->bitmap);
        m_windowsData->bitmap = nullptr;
    }

    if (m_windowsData->memoryDC)
    {
        DeleteDC(m_windowsData->memoryDC);
        m_windowsData->memoryDC = nullptr;
    }

    if (m_windowsData->desktopDC)
    {
        ReleaseDC(nullptr, m_windowsData->desktopDC);
        m_windowsData->desktopDC = nullptr;
    }
}

ScreenFrame ScreenCapture::captureScreen()
{
    EnterCriticalSection(&m_captureCriticalSection);
    ScreenFrame result;
    if (m_useCustomRegion)
    {
        result = captureRegion(m_captureRegion);
    }
    else
    {
        result = captureMonitor(m_activeMonitor);
    }

    LeaveCriticalSection(&m_captureCriticalSection);
    return result;
}

ScreenFrame ScreenCapture::captureMonitor(int monitorId)
{
    ScreenFrame frame;
    frame.monitorId = monitorId;

    // Debug: Log screen dimensions
    LOG_DEBUG("Screen dimensions: " + std::to_string(m_windowsData->screenWidth) + "x" + std::to_string(m_windowsData->screenHeight));

    // Validate screen dimensions
    if (m_windowsData->screenWidth <= 0 || m_windowsData->screenHeight <= 0)
    {
        LOG_ERROR("Invalid screen dimensions: " + std::to_string(m_windowsData->screenWidth) + "x" + std::to_string(m_windowsData->screenHeight));
        return frame;
    }

    frame.region = Rect(0, 0, m_windowsData->screenWidth, m_windowsData->screenHeight);
    frame.format = ScreenFormat::BGR24; // Change to BGR24 to match server's expected format
    frame.isFullFrame = true;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                          std::chrono::steady_clock::now().time_since_epoch())
                          .count();

    LOG_DEBUG("Frame region set to: " + std::to_string(frame.region.width) + "x" + std::to_string(frame.region.height));

    // Capture screen using BitBlt
    if (!BitBlt(m_windowsData->memoryDC, 0, 0,
                m_windowsData->screenWidth, m_windowsData->screenHeight,
                m_windowsData->desktopDC, 0, 0, SRCCOPY))
    {
        LOG_ERROR("BitBlt failed: " + std::to_string(GetLastError()));
        return frame;
    }

    // Get bitmap data
    if (GetDIBits(m_windowsData->desktopDC, m_windowsData->bitmap, 0,
                  m_windowsData->screenHeight, m_windowsData->bitmapData.data(),
                  &m_windowsData->bitmapInfo, DIB_RGB_COLORS) == 0)
    {
        LOG_ERROR("GetDIBits failed: " + std::to_string(GetLastError()));
        return frame;
    }

    // Process frame data - keep BGR format instead of converting to RGB
    std::vector<uint8_t> originalFrameData = convertBGRAtoBGR24(m_windowsData->bitmapData, m_windowsData->screenWidth, m_windowsData->screenHeight);

    LOG_DEBUG("Converted frame data from " + std::to_string(m_windowsData->bitmapData.size()) +
              " bytes (BGRA32) to " + std::to_string(originalFrameData.size()) + " bytes (BGR24)");

    // Apply image resizing if enabled
    int resizedWidth, resizedHeight;
    if (m_imageResizeEnabled)
    {
        frame.data = resizeImageByPercent(originalFrameData, m_windowsData->screenWidth, m_windowsData->screenHeight, m_imageScalePercent, resizedWidth, resizedHeight);

        // Update frame dimensions to reflect the resized image
        frame.region.width = resizedWidth;
        frame.region.height = resizedHeight;

        LOG_INFO("Frame resized from " + std::to_string(m_windowsData->screenWidth) + "x" + std::to_string(m_windowsData->screenHeight) +
                 " to " + std::to_string(resizedWidth) + "x" + std::to_string(resizedHeight) +
                 " (scale: " + std::to_string(static_cast<int>(m_imageScalePercent * 100)) + "%, data: " +
                 std::to_string(originalFrameData.size()) + " -> " + std::to_string(frame.data.size()) + " bytes)");
    }
    else
    {
        // No resizing, use original data
        frame.data = originalFrameData;
        resizedWidth = m_windowsData->screenWidth;
        resizedHeight = m_windowsData->screenHeight;

        LOG_INFO("Frame not resized, using original size: " + std::to_string(resizedWidth) + "x" + std::to_string(resizedHeight) +
                 " (data: " + std::to_string(frame.data.size()) + " bytes)");
    }

    // Save first few frames for debugging (only save first 5 frames to avoid too many files)
    static int frameCounter = 0;
    if (frameCounter < 5)
    {
        frameCounter++;

        // Generate filename with timestamp
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

        std::stringstream ss;
        ss << "frame_" << frameCounter << "_"
           << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
           << "_" << std::setfill('0') << std::setw(3) << ms.count();

        // Frame processing completed - no longer saving frames to disk
        LOG_DEBUG("Frame " + std::to_string(frameCounter) + " processed successfully");
    }
    if (m_compressionEnabled)
    {
        auto originalSize = frame.data.size();
        auto compressedData = compressFrame(frame.data, m_format);

        // Reduce logging frequency for better performance
        static int compressionLogCounter = 0;
        if (compressionLogCounter++ % 60 == 0)
        { // Log every 60 frames (1 second at 60fps)
            LOG_INFO("Compression attempted: original=" + std::to_string(originalSize) +
                     ", compressed=" + std::to_string(compressedData.size()) + " bytes");
        }

        // Check if compression was beneficial
        bool compressionBeneficial = (compressedData.size() < originalSize);

        if (compressionBeneficial)
        {
            // Use compressed data
            frame.originalSize = static_cast<uint32_t>(originalSize);
            frame.data = compressedData;
            frame.isCompressed = true;
        }
        else
        {
            // Keep original data
            frame.originalSize = 0;
            frame.isCompressed = false;
        }
    }
    else
    {
        frame.originalSize = 0;
        frame.isCompressed = false;
    }

    return frame;
}

ScreenFrame ScreenCapture::captureRegion(const Rect &region)
{
    // For simplicity, capture full screen and crop
    // In a real implementation, you would optimize this
    ScreenFrame fullFrame = captureMonitor(m_activeMonitor);

    // TODO: Implement region cropping
    fullFrame.region = region;
    return fullFrame;
}

ScreenFrame ScreenCapture::processFrame(const std::vector<uint8_t> &rawData, const Size &size)
{
    (void)size; // Suppress unused parameter warning
    ScreenFrame frame;
    frame.data = rawData;
    // TODO: Process frame data
    return frame;
}

std::vector<uint8_t> ScreenCapture::compressFrame(const std::vector<uint8_t> &data, ScreenFormat format)
{
    (void)format; // Suppress unused parameter warning
    if (!m_compressionManager || data.empty())
    {
        return data;
    }

    try
    {
        auto result = m_compressionManager->compressScreenData(data);

        if (result.success)
        {
            LOG_DEBUG("Compression result: " + std::to_string(result.originalSize) +
                      " -> " + std::to_string(result.compressedSize) +
                      " bytes (ratio: " + std::to_string(result.ratio) + ")");
            return result.data;
        }
        else
        {
            LOG_DEBUG("Compression failed, using original data");
            return data;
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Compression failed: " + std::string(e.what()));
        return data;
    }
}

ScreenFrame ScreenCapture::createDeltaFrame(const ScreenFrame &current, const ScreenFrame &previous)
{
    (void)previous; // Suppress unused parameter warning
    // TODO: Implement delta frame creation
    return current;
}

bool ScreenCapture::shouldSendFrame(const ScreenFrame &frame)
{
    // Always send first frame
    if (m_frameCount == 0)
    {
        return true;
    }

    // Skip frames if we're behind on processing
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastFrame = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastFrameTime);

    // If processing is taking too long, skip frames to maintain responsiveness
    if (timeSinceLastFrame.count() > (1000 / m_adaptiveFrameRate) * 1.5)
    {
        m_frameSkipCount++;
        LOG_DEBUG("Skipping frame due to processing delay: " + std::to_string(timeSinceLastFrame.count()) + "ms");
        return false;
    }

    // Basic frame difference check (simplified)
    if (m_lastFrame.data.size() == frame.data.size() && !m_lastFrame.data.empty())
    {
        // Calculate simple difference percentage
        size_t differences = 0;
        size_t sampleSize = std::min(frame.data.size(), static_cast<size_t>(1024)); // Sample first 1KB

        for (size_t i = 0; i < sampleSize; i += 3) // Sample every 3rd byte for performance
        {
            if (abs(static_cast<int>(frame.data[i]) - static_cast<int>(m_lastFrame.data[i])) > 10)
            {
                differences++;
            }
        }

        float changePercentage = static_cast<float>(differences) / (sampleSize / 3) * 100.0f;

        // Skip frame if change is minimal (less than 2%)
        if (changePercentage < 2.0f)
        {
            LOG_DEBUG("Skipping frame due to minimal change: " + std::to_string(changePercentage) + "%");
            return false;
        }
    }

    // Store current frame for next comparison
    m_lastFrame = frame;
    return true;
}

void ScreenCapture::adjustQuality()
{
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastAdjustment = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_lastQualityAdjustment);

    // Only adjust quality every 2 seconds to avoid oscillation
    if (timeSinceLastAdjustment.count() < 2000)
    {
        return;
    }

    m_lastQualityAdjustment = now;

    // Calculate average frame size over recent frames
    if (m_frameCount > 0)
    {
        int avgFrameSize = m_totalFrameSize / m_frameCount;

        // Adjust quality based on frame size and target
        if (avgFrameSize > m_maxFrameSize * 0.8) // If frames are getting too large
        {
            m_quality = std::max(30, m_quality - 10); // Reduce quality but not below 30
            LOG_INFO("Reducing quality to " + std::to_string(m_quality) + " due to large frame size: " + std::to_string(avgFrameSize));
        }
        else if (avgFrameSize < m_maxFrameSize * 0.3) // If frames are small, we can increase quality
        {
            m_quality = std::min(90, m_quality + 5); // Increase quality but not above 90
            LOG_INFO("Increasing quality to " + std::to_string(m_quality) + " due to small frame size: " + std::to_string(avgFrameSize));
        }
    }
}

void ScreenCapture::updateAdaptiveFrameRate()
{
    // Calculate current CPU usage and network performance
    auto now = std::chrono::steady_clock::now();
    auto timeSinceStart = std::chrono::duration_cast<std::chrono::seconds>(now - m_captureStartTime);

    if (timeSinceStart.count() < 5) // Don't adjust for first 5 seconds
    {
        return;
    }

    // Calculate actual frame rate
    if (m_frameCount > 0 && timeSinceStart.count() > 0)
    {
        m_currentFrameRate = m_frameCount / static_cast<int>(timeSinceStart.count());
    }

    // Adjust adaptive frame rate based on performance
    if (m_frameSkipCount > m_frameCount * 0.1) // If skipping more than 10% of frames
    {
        m_adaptiveFrameRate = std::max(MIN_FRAME_RATE, m_adaptiveFrameRate - 5);
        LOG_INFO("Reducing adaptive frame rate to " + std::to_string(m_adaptiveFrameRate) + " due to frame skipping");
        m_frameSkipCount = 0; // Reset counter
    }
    else if (m_frameSkipCount == 0 && m_adaptiveFrameRate < m_frameRate)
    {
        m_adaptiveFrameRate = std::min(m_frameRate, m_adaptiveFrameRate + 2);
        LOG_INFO("Increasing adaptive frame rate to " + std::to_string(m_adaptiveFrameRate));
    }
}

int ScreenCapture::calculateOptimalQuality(int frameSize, int targetSize)
{
    (void)frameSize;  // Suppress unused parameter warning
    (void)targetSize; // Suppress unused parameter warning
    // TODO: Implement quality calculation
    return m_quality;
}

void ScreenCapture::updateFrameRateStats()
{
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_captureStartTime);

    if (elapsed.count() > 0)
    {
        m_currentFrameRate = m_frameCount / static_cast<int>(elapsed.count());
    }
}

void ScreenCapture::detectMonitors()
{
    m_monitors.clear();

    // Get primary monitor info
    MonitorInfo primaryMonitor;
    primaryMonitor.id = 0;
    primaryMonitor.bounds = Rect(0, 0, GetSystemMetrics(SM_CXSCREEN), GetSystemMetrics(SM_CYSCREEN));
    primaryMonitor.primary = true;
    primaryMonitor.refreshRate = 60; // Default
    primaryMonitor.name = "Primary Monitor";

    m_monitors.push_back(primaryMonitor);

    // Get all monitors using EnumDisplayMonitors
    EnumDisplayMonitors(nullptr, nullptr, [](HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) -> BOOL
                        {
        auto* monitors = reinterpret_cast<std::vector<MonitorInfo>*>(dwData);

        MONITORINFOEX monitorInfo;
        monitorInfo.cbSize = sizeof(MONITORINFOEX);
        if (GetMonitorInfo(hMonitor, &monitorInfo)) {
            MonitorInfo monitor;
            monitor.id = static_cast<int>(monitors->size());
            monitor.bounds = Rect(
                monitorInfo.rcMonitor.left,
                monitorInfo.rcMonitor.top,
                monitorInfo.rcMonitor.right - monitorInfo.rcMonitor.left,
                monitorInfo.rcMonitor.bottom - monitorInfo.rcMonitor.top
            );
            monitor.primary = (monitorInfo.dwFlags & MONITORINFOF_PRIMARY) != 0;
            monitor.refreshRate = 60; // Default

            // Convert device name to std::string
            monitor.name = std::string(monitorInfo.szDevice);

            monitors->push_back(monitor);
        }
        return TRUE; }, reinterpret_cast<LPARAM>(&m_monitors));

    LOG_INFO("Detected " + std::to_string(m_monitors.size()) + " monitor(s)");
    for (const auto &monitor : m_monitors)
    {
        LOG_INFO("Monitor " + std::to_string(monitor.id) + ": " +
                 std::to_string(monitor.bounds.width) + "x" + std::to_string(monitor.bounds.height) +
                 (monitor.primary ? " (Primary)" : ""));
    }
}

void ScreenCapture::updateMonitorInfo()
{
    // TODO: Update monitor information
}

void ScreenCapture::updatePerformanceStats()
{
    updateFrameRateStats();

    if (m_frameCount > 0)
    {
        m_averageFrameSize = m_totalFrameSize / m_frameCount;
    }

    m_cpuUsage = calculateCpuUsage();
}

double ScreenCapture::calculateCpuUsage()
{
    // TODO: Implement CPU usage calculation
    return 0.0;
}

std::vector<uint8_t> ScreenCapture::convertBGRAtoRGB24(const std::vector<uint8_t> &bgraData, int width, int height)
{
    // Validate input parameters
    if (bgraData.empty() || width <= 0 || height <= 0)
    {
        LOG_ERROR("Invalid parameters for BGRA to RGB24 conversion");
        return std::vector<uint8_t>();
    }

    // Calculate expected size
    size_t expectedSize = width * height * 4;
    if (bgraData.size() < expectedSize)
    {
        LOG_ERROR("BGRA data size mismatch: expected " + std::to_string(expectedSize) +
                  " bytes, got " + std::to_string(bgraData.size()) + " bytes");
        return std::vector<uint8_t>();
    }

    // Convert BGRA32 (4 bytes per pixel) to RGB24 (3 bytes per pixel)
    std::vector<uint8_t> rgb24Data;
    rgb24Data.reserve(width * height * 3);

    // Convert BGRA to RGB24 (swap R and B, remove alpha)
    for (int i = 0; i < bgraData.size(); i += 4)
    {
        // BGRA to RGB (swap B and R, remove A)
        rgb24Data.push_back(bgraData[i + 2]); // R (from BGRA[2])
        rgb24Data.push_back(bgraData[i + 1]); // G (same)
        rgb24Data.push_back(bgraData[i]);     // B (from BGRA[0])
        // Alpha is discarded
    }

    LOG_DEBUG("Converted BGRA to RGB24: " + std::to_string(bgraData.size()) +
              " -> " + std::to_string(rgb24Data.size()) + " bytes");

    return rgb24Data;
}

std::vector<uint8_t> ScreenCapture::convertBGRAtoBGR24(const std::vector<uint8_t> &bgraData, int width, int height)
{
    // Validate input parameters
    if (bgraData.empty() || width <= 0 || height <= 0)
    {
        LOG_ERROR("Invalid parameters for BGRA to BGR24 conversion");
        return std::vector<uint8_t>();
    }

    // Calculate expected size
    size_t expectedSize = width * height * 4;
    if (bgraData.size() < expectedSize)
    {
        LOG_ERROR("BGRA data size mismatch: expected " + std::to_string(expectedSize) +
                  " bytes, got " + std::to_string(bgraData.size()) + " bytes");
        return std::vector<uint8_t>();
    }

    // Convert BGRA32 (4 bytes per pixel) to BGR24 (3 bytes per pixel)
    std::vector<uint8_t> bgr24Data;
    bgr24Data.reserve(width * height * 3);

    // Convert BGRA to BGR24 (just remove alpha channel)
    for (int i = 0, j = 0; i < bgraData.size(); i += 4, j += 3)
    {
        // BGRA to BGR (keep BGR order, just remove A)
        bgr24Data.push_back(bgraData[i]);     // B (same)
        bgr24Data.push_back(bgraData[i + 1]); // G (same)
        bgr24Data.push_back(bgraData[i + 2]); // R (same)
        // Alpha is discarded
    }

    LOG_DEBUG("First 3 pixels BGR values: (" +
              std::to_string(bgr24Data[0]) + "," + std::to_string(bgr24Data[1]) + "," + std::to_string(bgr24Data[2]) + ") (" +
              std::to_string(bgr24Data[3]) + "," + std::to_string(bgr24Data[4]) + "," + std::to_string(bgr24Data[5]) + ") (" +
              std::to_string(bgr24Data[6]) + "," + std::to_string(bgr24Data[7]) + "," + std::to_string(bgr24Data[8]) + ")");

    return bgr24Data;
}

std::vector<uint8_t> ScreenCapture::resizeImage(const std::vector<uint8_t> &imageData, int originalWidth, int originalHeight, int newWidth, int newHeight)
{
    if (imageData.empty() || originalWidth <= 0 || originalHeight <= 0 || newWidth <= 0 || newHeight <= 0)
    {
        LOG_ERROR("Invalid parameters for image resize");
        return {};
    }

    // Calculate expected sizes
    size_t originalSize = originalWidth * originalHeight * 3; // BGR24 = 3 bytes per pixel
    size_t newSize = newWidth * newHeight * 3;

    if (imageData.size() != originalSize)
    {
        LOG_ERROR("Image data size mismatch: expected " + std::to_string(originalSize) +
                  " bytes, got " + std::to_string(imageData.size()) + " bytes");
        return {};
    }

    std::vector<uint8_t> resizedData(newSize);

    // Simple nearest neighbor interpolation
    for (int y = 0; y < newHeight; y++)
    {
        for (int x = 0; x < newWidth; x++)
        {
            // Map new coordinates to original coordinates
            int srcX = (x * originalWidth) / newWidth;
            int srcY = (y * originalHeight) / newHeight;

            // Ensure we don't go out of bounds
            srcX = std::min(srcX, originalWidth - 1);
            srcY = std::min(srcY, originalHeight - 1);

            // Calculate pixel indices
            int srcIndex = (srcY * originalWidth + srcX) * 3;
            int dstIndex = (y * newWidth + x) * 3;

            // Copy BGR values
            resizedData[dstIndex] = imageData[srcIndex];         // B
            resizedData[dstIndex + 1] = imageData[srcIndex + 1]; // G
            resizedData[dstIndex + 2] = imageData[srcIndex + 2]; // R
        }
    }

    LOG_DEBUG("Resized image from " + std::to_string(originalWidth) + "x" + std::to_string(originalHeight) +
              " to " + std::to_string(newWidth) + "x" + std::to_string(newHeight) +
              " (data: " + std::to_string(imageData.size()) + " -> " + std::to_string(resizedData.size()) + " bytes)");

    return resizedData;
}

std::vector<uint8_t> ScreenCapture::resizeImageBy50Percent(const std::vector<uint8_t> &imageData, int originalWidth, int originalHeight, int &newWidth, int &newHeight)
{
    return resizeImageByPercent(imageData, originalWidth, originalHeight, 0.5f, newWidth, newHeight);
}

std::vector<uint8_t> ScreenCapture::resizeImageByPercent(const std::vector<uint8_t> &imageData, int originalWidth, int originalHeight, float scalePercent, int &newWidth, int &newHeight)
{
    // Validate scale percent
    if (scalePercent <= 0.0f || scalePercent > 2.0f)
    {
        LOG_WARNING("Invalid scale percent: " + std::to_string(scalePercent) + ", using default 0.5");
        scalePercent = 0.5f;
    }

    // Calculate new dimensions
    newWidth = static_cast<int>(originalWidth * scalePercent);
    newHeight = static_cast<int>(originalHeight * scalePercent);

    // Ensure minimum size
    if (newWidth < 1)
        newWidth = 1;
    if (newHeight < 1)
        newHeight = 1;

    // Ensure maximum size (don't exceed original)
    if (newWidth > originalWidth)
        newWidth = originalWidth;
    if (newHeight > originalHeight)
        newHeight = originalHeight;

    LOG_INFO("Resizing image by " + std::to_string(static_cast<int>(scalePercent * 100)) + "%: " +
             std::to_string(originalWidth) + "x" + std::to_string(originalHeight) +
             " -> " + std::to_string(newWidth) + "x" + std::to_string(newHeight));

    return resizeImage(imageData, originalWidth, originalHeight, newWidth, newHeight);
}

// Image saving functionality removed

// Bitmap saving functionality removed
