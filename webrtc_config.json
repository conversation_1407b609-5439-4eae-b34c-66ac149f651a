{"webrtc": {"enabled": true, "signaling": {"server_url": "ws://localhost", "server_port": 8080, "connection_timeout": 10000, "heartbeat_interval": 30000, "reconnect_attempts": 3, "reconnect_delay": 5000}, "ice_servers": [{"urls": "stun:stun.l.google.com:19302"}, {"urls": "stun:stun1.l.google.com:19302"}, {"urls": "stun:stun2.l.google.com:19302"}, {"urls": "stun:stun3.l.google.com:19302"}, {"urls": "stun:stun4.l.google.com:19302"}], "data_channels": {"enabled": true, "ordered": true, "max_retransmits": 3, "max_packet_life_time": 3000, "protocol": "superbot-v1"}, "peer_connection": {"ice_candidate_pool_size": 10, "ice_connection_receiving_timeout": 30000, "ice_backup_candidate_pair_ping_interval": 25000, "key_type": "ECDSA", "ice_check_min_interval": 2000, "stun_candidate_keepalive_interval": 10000}, "media": {"enable_audio": false, "enable_video": false, "enable_data_channel_only": true}, "logging": {"enabled": true, "level": "INFO", "log_to_file": true, "log_file_path": "logs/webrtc.log", "max_log_file_size": 10485760, "max_log_files": 5}, "performance": {"enable_cpu_adaptation": true, "enable_bandwidth_adaptation": true, "max_cpu_consumption_percentage": 80, "min_bandwidth_bps": 100000, "max_bandwidth_bps": 10000000, "start_bandwidth_bps": 1000000}, "security": {"enable_dtls": true, "enable_srtp": false, "dtls_cert_verify": false, "enable_rtp_data_channel": false}, "fallback": {"enable_tcp_fallback": true, "tcp_fallback_timeout": 15000, "enable_relay_fallback": true, "relay_fallback_timeout": 20000}, "quality_control": {"enable_adaptive_bitrate": true, "min_bitrate_kbps": 100, "max_bitrate_kbps": 5000, "start_bitrate_kbps": 1000, "enable_frame_dropping": true, "max_frame_rate": 60, "min_frame_rate": 10, "target_frame_rate": 30}, "compression": {"enable_data_compression": true, "compression_algorithm": "LZ4", "compression_level": 1, "min_compression_size": 1024}, "timeouts": {"connection_timeout": 30000, "data_channel_timeout": 10000, "ice_gathering_timeout": 10000, "offer_answer_timeout": 10000, "peer_connection_timeout": 30000}, "limits": {"max_message_size": 65536, "max_pending_messages": 1000, "max_data_channel_buffer": 16777216, "max_ice_candidates": 50}, "experimental": {"enable_unified_plan": true, "enable_dtls_srtp": true, "enable_rtp_data_channel": false, "enable_sctp_data_channel": true, "enable_plan_b": false}}, "network": {"protocol": "WebRTC", "fallback_protocol": "TCP", "auto_protocol_selection": true, "prefer_webrtc": true, "tcp_port": 7878, "enable_upnp": false, "enable_nat_traversal": true}, "application": {"peer_id": "", "auto_generate_peer_id": true, "peer_id_prefix": "superbot-client", "enable_peer_discovery": true, "discovery_timeout": 5000}}